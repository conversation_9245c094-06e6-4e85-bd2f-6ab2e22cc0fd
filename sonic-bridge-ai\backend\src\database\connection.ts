// SoundBridge AI Database Connection Management
import { PrismaClient } from '@prisma/client';
import { createClient } from 'redis';
import { logger } from '../middleware/logger';

// Global variable to store the Prisma client instance
declare global {
  var __prisma: PrismaClient | undefined;
  var __redis: ReturnType<typeof createClient> | undefined;
}



// Create Prisma client with connection pooling
function createPrismaClient(): PrismaClient {
  if (process.env.NODE_ENV === 'development') {
    return new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
      errorFormat: 'pretty',
    });
  } else {
    return new PrismaClient({
      log: ['error'],
      errorFormat: 'pretty',
    });
  }
}

// Singleton pattern for Prisma client
export const prisma = globalThis.__prisma || createPrismaClient();

// In development, store the client globally to prevent multiple instances
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

// Database connection health check
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    logger.warn('Database connection failed - this is expected if running without Docker', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return false;
  }
}

// Create Redis client with connection pooling
function createRedisClient() {
  const redisUrl = process.env.REDIS_URL;

  // If no Redis URL is provided, return a mock client for local development
  if (!redisUrl || redisUrl.trim() === '') {
    return null;
  }

  const client = createClient({
    url: redisUrl,
    socket: {
      reconnectStrategy: (retries) => Math.min(retries * 50, 500),
    },
  });

  client.on('error', (err) => {
    logger.error('Redis Client Error:', err);
  });

  client.on('connect', () => {
    logger.info('Redis client connected');
  });

  client.on('ready', () => {
    logger.info('Redis client ready');
  });

  client.on('end', () => {
    logger.info('Redis client disconnected');
  });

  return client;
}

// Singleton pattern for Redis client
export const redis = globalThis.__redis || createRedisClient();

// In development, store the client globally to prevent multiple instances
if (process.env.NODE_ENV === 'development') {
  globalThis.__redis = redis as any;
}

// Initialize Redis connection
export async function connectRedis(): Promise<void> {
  try {
    if (!redis) {
      logger.info('Redis disabled for local development');
      return;
    }

    if (!redis.isOpen) {
      await redis.connect();
      logger.info('Redis connection established');
    }
  } catch (error) {
    logger.warn('Redis connection failed - this is expected if running without Docker', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    // Don't throw error, allow server to continue without Redis
  }
}

// Redis connection health check
export async function checkRedisConnection(): Promise<boolean> {
  try {
    if (!redis) {
      logger.info('Redis disabled - skipping health check');
      return false;
    }

    await redis.ping();
    return true;
  } catch (error) {
    logger.warn('Redis health check failed - this is expected if running without Docker', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return false;
  }
}

// Graceful shutdown handler
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    console.log('Database connection closed gracefully');
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
}

// Graceful Redis shutdown handler
export async function disconnectRedis(): Promise<void> {
  try {
    if (redis.isOpen) {
      await redis.disconnect();
      logger.info('Redis connection closed gracefully');
    }
  } catch (error) {
    logger.error('Error closing Redis connection:', error);
  }
}

// Database metrics and monitoring
export async function getDatabaseMetrics() {
  try {
    const [userCount, artistCount, trackCount, playlistCount] = await Promise.all([
      prisma.user.count(),
      prisma.artist.count(),
      prisma.track.count(),
      prisma.playlist.count(),
    ]);

    return {
      users: userCount,
      artists: artistCount,
      tracks: trackCount,
      playlists: playlistCount,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('Error fetching database metrics:', error);
    throw error;
  }
}

// Transaction helper
export async function withTransaction<T>(
  callback: (tx: Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>) => Promise<T>
): Promise<T> {
  return await prisma.$transaction(callback);
}

// Database initialization
export async function initializeDatabase(): Promise<void> {
  try {
    console.log('🔌 Connecting to database...');
    
    // Test connection
    const isConnected = await checkDatabaseConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to database');
    }

    console.log('✅ Database connection established');

    // Log database metrics
    const metrics = await getDatabaseMetrics();
    console.log('📊 Database metrics:', metrics);

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
}

// Cleanup function for graceful shutdown
process.on('SIGINT', async () => {
  console.log('🛑 Received SIGINT, closing database connection...');
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🛑 Received SIGTERM, closing database connection...');
  await disconnectDatabase();
  process.exit(0);
});

export default prisma;
