/**
 * SoundBridge AI - OAuth Integration Testing Script
 * Tests Google and Spotify OAuth flows with real accounts
 * 
 * Usage: node oauth-integration-test.js
 */

const axios = require('axios');
const readline = require('readline');
const { URL } = require('url');

// Configuration
const BASE_URL = 'http://localhost:3002';
const FRONTEND_URL = 'http://localhost:8080';

// Test state
let testResults = {
  google: {
    authUrl: null,
    callback: null,
    userProfile: null,
    token: null
  },
  spotify: {
    authUrl: null,
    callback: null,
    userProfile: null,
    token: null
  }
};

// Utility functions
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (prompt) => {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
};

const logStep = (step, status, details = '') => {
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⏳';
  console.log(`${statusIcon} ${step}${details ? ': ' + details : ''}`);
};

const logHeader = (title) => {
  console.log('\n' + '='.repeat(60));
  console.log(`🧪 ${title}`);
  console.log('='.repeat(60));
};

// Test functions
async function testServerHealth() {
  logHeader('Server Health Check');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/health`);
    logStep('Backend server health', 'PASS', `Status: ${response.status}`);
    return true;
  } catch (error) {
    logStep('Backend server health', 'FAIL', error.message);
    return false;
  }
}

async function testGoogleOAuthFlow() {
  logHeader('Google OAuth Integration Test');
  
  try {
    // Step 1: Generate Google OAuth URL
    logStep('Generating Google OAuth URL', 'PENDING');
    const authResponse = await axios.post(`${BASE_URL}/auth/google/login`, {
      action: 'login',
      redirectUrl: FRONTEND_URL
    });
    
    testResults.google.authUrl = authResponse.data.data.authUrl;
    logStep('Google OAuth URL generated', 'PASS');
    
    // Step 2: Display URL for manual testing
    console.log('\n📋 Google OAuth URL:');
    console.log(testResults.google.authUrl);
    console.log('\n🔗 Please:');
    console.log('1. Open the above URL in your browser');
    console.log('2. Sign in with your Gmail account');
    console.log('3. Grant permissions');
    console.log('4. Copy the callback URL from the browser');
    
    const callbackUrl = await question('\n📥 Paste the callback URL here: ');
    
    // Step 3: Parse callback URL
    const url = new URL(callbackUrl);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');
    
    if (!code || !state) {
      throw new Error('Missing code or state parameter in callback URL');
    }
    
    logStep('Callback URL parsed', 'PASS', `Code: ${code.substring(0, 20)}...`);
    
    // Step 4: Test callback endpoint
    logStep('Processing Google OAuth callback', 'PENDING');
    const callbackResponse = await axios.get(`${BASE_URL}/auth/google/callback`, {
      params: { code, state }
    });
    
    testResults.google.callback = callbackResponse.data;
    testResults.google.token = callbackResponse.data.data?.token;
    
    logStep('Google OAuth callback processed', 'PASS');
    logStep('JWT token received', 'PASS', `Token: ${testResults.google.token?.substring(0, 20)}...`);
    
    return true;
  } catch (error) {
    logStep('Google OAuth flow', 'FAIL', error.response?.data?.message || error.message);
    return false;
  }
}

async function testSpotifyOAuthFlow() {
  logHeader('Spotify OAuth Integration Test');
  
  try {
    // Step 1: Generate Spotify OAuth URL
    logStep('Generating Spotify OAuth URL', 'PENDING');
    const authResponse = await axios.post(`${BASE_URL}/auth/spotify/login`, {
      action: 'login',
      redirectUrl: FRONTEND_URL
    });
    
    testResults.spotify.authUrl = authResponse.data.data.authUrl;
    logStep('Spotify OAuth URL generated', 'PASS');
    
    // Step 2: Display URL for manual testing
    console.log('\n📋 Spotify OAuth URL:');
    console.log(testResults.spotify.authUrl);
    console.log('\n🔗 Please:');
    console.log('1. Open the above URL in your browser');
    console.log('2. Sign in with your Spotify account');
    console.log('3. Grant permissions');
    console.log('4. Copy the callback URL from the browser');
    
    const callbackUrl = await question('\n📥 Paste the callback URL here: ');
    
    // Step 3: Parse callback URL
    const url = new URL(callbackUrl);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');
    
    if (!code || !state) {
      throw new Error('Missing code or state parameter in callback URL');
    }
    
    logStep('Callback URL parsed', 'PASS', `Code: ${code.substring(0, 20)}...`);
    
    // Step 4: Test callback endpoint
    logStep('Processing Spotify OAuth callback', 'PENDING');
    const callbackResponse = await axios.get(`${BASE_URL}/auth/spotify/callback`, {
      params: { code, state }
    });
    
    testResults.spotify.callback = callbackResponse.data;
    testResults.spotify.token = callbackResponse.data.data?.token;
    
    logStep('Spotify OAuth callback processed', 'PASS');
    logStep('JWT token received', 'PASS', `Token: ${testResults.spotify.token?.substring(0, 20)}...`);
    
    return true;
  } catch (error) {
    logStep('Spotify OAuth flow', 'FAIL', error.response?.data?.message || error.message);
    return false;
  }
}

async function testTokenValidation() {
  logHeader('Token Validation Tests');
  
  // Test Google token
  if (testResults.google.token) {
    try {
      const response = await axios.get(`${BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${testResults.google.token}`
        }
      });
      
      testResults.google.userProfile = response.data.data;
      logStep('Google token validation', 'PASS', `User: ${response.data.data.email}`);
    } catch (error) {
      logStep('Google token validation', 'FAIL', error.response?.data?.message || error.message);
    }
  }
  
  // Test Spotify token
  if (testResults.spotify.token) {
    try {
      const response = await axios.get(`${BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${testResults.spotify.token}`
        }
      });
      
      testResults.spotify.userProfile = response.data.data;
      logStep('Spotify token validation', 'PASS', `User: ${response.data.data.email}`);
    } catch (error) {
      logStep('Spotify token validation', 'FAIL', error.response?.data?.message || error.message);
    }
  }
}

async function testAccountLinking() {
  logHeader('Account Linking Tests');
  
  if (testResults.google.token && testResults.spotify.token) {
    console.log('🔗 Both Google and Spotify tokens available for linking test');
    
    // Test linking Spotify to Google account
    try {
      const linkResponse = await axios.post(`${BASE_URL}/auth/spotify/link-account`, {
        userId: testResults.google.userProfile.id
      }, {
        headers: {
          'Authorization': `Bearer ${testResults.google.token}`
        }
      });
      
      logStep('Account linking (Spotify to Google)', 'PASS');
    } catch (error) {
      logStep('Account linking (Spotify to Google)', 'FAIL', error.response?.data?.message || error.message);
    }
  } else {
    logStep('Account linking', 'SKIP', 'Requires both Google and Spotify tokens');
  }
}

async function generateTestReport() {
  logHeader('Test Results Summary');
  
  console.log('\n📊 OAuth Integration Test Results:');
  console.log('─'.repeat(50));
  
  // Google OAuth Results
  console.log('\n🔵 Google OAuth:');
  console.log(`   Auth URL: ${testResults.google.authUrl ? '✅ Generated' : '❌ Failed'}`);
  console.log(`   Callback: ${testResults.google.callback ? '✅ Success' : '❌ Failed'}`);
  console.log(`   Token: ${testResults.google.token ? '✅ Received' : '❌ Missing'}`);
  console.log(`   Profile: ${testResults.google.userProfile ? '✅ Retrieved' : '❌ Missing'}`);
  
  // Spotify OAuth Results
  console.log('\n🟢 Spotify OAuth:');
  console.log(`   Auth URL: ${testResults.spotify.authUrl ? '✅ Generated' : '❌ Failed'}`);
  console.log(`   Callback: ${testResults.spotify.callback ? '✅ Success' : '❌ Failed'}`);
  console.log(`   Token: ${testResults.spotify.token ? '✅ Received' : '❌ Missing'}`);
  console.log(`   Profile: ${testResults.spotify.userProfile ? '✅ Retrieved' : '❌ Missing'}`);
  
  // Overall Status
  const googleSuccess = testResults.google.token && testResults.google.userProfile;
  const spotifySuccess = testResults.spotify.token && testResults.spotify.userProfile;
  
  console.log('\n🎯 Overall Status:');
  console.log(`   Google OAuth: ${googleSuccess ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Spotify OAuth: ${spotifySuccess ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Integration: ${googleSuccess && spotifySuccess ? '✅ READY FOR PRODUCTION' : '⚠️ NEEDS ATTENTION'}`);
}

// Main test execution
async function runOAuthTests() {
  console.log('🚀 SoundBridge AI - OAuth Integration Testing');
  console.log('Testing Google and Spotify OAuth flows with real accounts\n');
  
  try {
    // Check server health
    const serverHealthy = await testServerHealth();
    if (!serverHealthy) {
      console.log('\n❌ Server is not running. Please start the backend server first.');
      console.log('Run: cd sonic-bridge-ai/backend && npm run dev');
      process.exit(1);
    }
    
    // Test OAuth flows
    await testGoogleOAuthFlow();
    await testSpotifyOAuthFlow();
    
    // Test token validation
    await testTokenValidation();
    
    // Test account linking
    await testAccountLinking();
    
    // Generate report
    await generateTestReport();
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
  } finally {
    rl.close();
  }
}

// Run tests
if (require.main === module) {
  runOAuthTests();
}

module.exports = {
  runOAuthTests,
  testResults
};
