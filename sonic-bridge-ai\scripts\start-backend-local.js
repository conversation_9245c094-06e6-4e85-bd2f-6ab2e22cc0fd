/**
 * SoundBridge AI - Local Backend Startup Script
 * Starts the backend server with minimal dependencies for OAuth testing
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting SoundBridge AI Backend in Local Mode');
console.log('This mode is optimized for OAuth testing without Docker dependencies\n');

// Check if we're in the right directory
const backendDir = path.join(__dirname, '../backend');
if (!fs.existsSync(backendDir)) {
  console.error('❌ Backend directory not found. Please run this script from the project root.');
  process.exit(1);
}

// Create a minimal .env for local testing
const envPath = path.join(backendDir, '.env.local');
const envContent = `
# SoundBridge AI - Local Testing Environment
NODE_ENV=development
PORT=3002
API_PORT=3002

# Database - Using SQLite for local testing
DATABASE_URL="file:./dev.db"

# Redis - Disabled for local testing
REDIS_URL=""
REDIS_HOST=""
REDIS_PORT=""

# Security
JWT_SECRET=local_development_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=10

# CORS
CORS_ORIGIN=http://localhost:8080,http://localhost:3000

# Google OAuth (configured by user)
GOOGLE_CLIENT_ID=${process.env.GOOGLE_CLIENT_ID || '729021649794-9eq4r13u6he4jg8i99dt4ls9f617fjfs.apps.googleusercontent.com'}
GOOGLE_CLIENT_SECRET=${process.env.GOOGLE_CLIENT_SECRET || 'placeholder_google_client_secret'}
GOOGLE_REDIRECT_URI=http://localhost:3002/auth/google/callback

# Spotify OAuth (to be configured)
SPOTIFY_CLIENT_ID=${process.env.SPOTIFY_CLIENT_ID || 'placeholder_spotify_client_id'}
SPOTIFY_CLIENT_SECRET=${process.env.SPOTIFY_CLIENT_SECRET || 'placeholder_spotify_client_secret'}
SPOTIFY_REDIRECT_URI=http://localhost:3002/auth/spotify/callback

# Logging
LOG_LEVEL=debug

# Feature flags for local testing
ENABLE_AI_FEATURES=false
ENABLE_VIDEO_INTEGRATION=false
ENABLE_ANALYTICS=false
ENABLE_NOTIFICATIONS=false
`;

// Write the local environment file
fs.writeFileSync(envPath, envContent);
console.log('✅ Created local environment configuration');

// Update Prisma schema for SQLite
const schemaPath = path.join(backendDir, 'prisma/schema.prisma');
if (fs.existsSync(schemaPath)) {
  let schemaContent = fs.readFileSync(schemaPath, 'utf8');
  
  // Replace PostgreSQL with SQLite for local testing
  schemaContent = schemaContent.replace(
    'provider = "postgresql"',
    'provider = "sqlite"'
  );
  
  // Comment out PostgreSQL-specific features
  schemaContent = schemaContent.replace(
    /@@index\(\[.*?\]\)/g,
    '// @@index([]) // Disabled for SQLite'
  );
  
  fs.writeFileSync(path.join(backendDir, 'prisma/schema.local.prisma'), schemaContent);
  console.log('✅ Created SQLite-compatible Prisma schema');
}

// Function to start the backend
function startBackend() {
  console.log('\n🔄 Starting backend server...');
  
  const backend = spawn('npm', ['run', 'dev'], {
    cwd: backendDir,
    stdio: 'inherit',
    shell: true,
    env: {
      ...process.env,
      NODE_ENV: 'development',
      DATABASE_URL: 'file:./dev.db',
      PORT: '3002'
    }
  });

  backend.on('error', (error) => {
    console.error('❌ Failed to start backend:', error.message);
    process.exit(1);
  });

  backend.on('close', (code) => {
    if (code !== 0) {
      console.error(`❌ Backend process exited with code ${code}`);
      process.exit(1);
    }
  });

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down backend server...');
    backend.kill('SIGINT');
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down backend server...');
    backend.kill('SIGTERM');
    process.exit(0);
  });
}

// Instructions for the user
console.log('\n📋 Local Backend Setup Complete!');
console.log('');
console.log('🔧 Configuration:');
console.log('   • Port: 3002');
console.log('   • Database: SQLite (dev.db)');
console.log('   • Redis: Disabled');
console.log('   • Environment: .env.local');
console.log('');
console.log('🔗 URLs:');
console.log('   • API Base: http://localhost:3002/api');
console.log('   • Health Check: http://localhost:3002/api/health');
console.log('   • API Docs: http://localhost:3002/docs');
console.log('');
console.log('🔐 OAuth Testing:');
console.log('   • Google OAuth: Configured with your Client ID');
console.log('   • Spotify OAuth: Needs Client Secret');
console.log('');
console.log('⚠️  Note: This is a minimal setup for OAuth testing only.');
console.log('   For full development, use Docker: npm run docker:up');
console.log('');

// Start the backend
startBackend();
