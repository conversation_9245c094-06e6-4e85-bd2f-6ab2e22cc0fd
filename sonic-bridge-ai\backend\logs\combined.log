{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T21:45:24.569Z"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T21:45:54.931Z"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T21:46:58.082Z"}
{"environment":"development","level":"info","message":"🚀 SoundBridge AI Backend started successfully","port":"3002","service":"soundbridge-backend","timestamp":"2025-06-09T21:46:58.175Z","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"soundbridge-backend","timestamp":"2025-06-09T21:47:34.190Z"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T21:49:19.750Z"}
{"environment":"development","level":"info","message":"🚀 SoundBridge AI Backend started successfully","port":"3002","service":"soundbridge-backend","timestamp":"2025-06-09T21:49:19.894Z","version":"1.0.0"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T21:49:38.947Z","url":"/api","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":200,"timestamp":"2025-06-09T21:49:38.960Z","url":"/"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T21:49:45.314Z","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dbConnected":true,"level":"info","message":"Health check performed","service":"soundbridge-backend","status":"healthy","timestamp":"2025-06-09T21:49:45.365Z","uptime":27.4569381}
{"duration":"53ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":200,"timestamp":"2025-06-09T21:49:45.367Z","url":"/"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T21:49:51.063Z","url":"/docs","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":301,"timestamp":"2025-06-09T21:49:51.077Z","url":"/"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T21:49:51.091Z","url":"/docs/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":200,"timestamp":"2025-06-09T21:49:51.095Z","url":"/"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T21:49:51.223Z","url":"/docs/swagger-ui.css","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T21:49:51.230Z","url":"/docs/swagger-ui-bundle.js","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T21:49:51.244Z","url":"/docs/swagger-ui-standalone-preset.js","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T21:49:51.247Z","url":"/docs/swagger-ui-init.js","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":200,"timestamp":"2025-06-09T21:49:51.249Z","url":"/swagger-ui-init.js"}
{"duration":"56ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":200,"timestamp":"2025-06-09T21:49:51.279Z","url":"/swagger-ui.css"}
{"duration":"80ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":200,"timestamp":"2025-06-09T21:49:51.324Z","url":"/swagger-ui-standalone-preset.js"}
{"duration":"209ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":200,"timestamp":"2025-06-09T21:49:51.439Z","url":"/swagger-ui-bundle.js"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T21:49:52.053Z","url":"/docs/favicon-32x32.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":200,"timestamp":"2025-06-09T21:49:52.066Z","url":"/favicon-32x32.png"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T21:54:07.915Z"}
{"environment":"development","level":"info","message":"🚀 SoundBridge AI Backend started successfully","port":"3002","service":"soundbridge-backend","timestamp":"2025-06-09T21:54:08.275Z","version":"1.0.0"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T22:52:43.900Z"}
{"level":"info","message":"Redis client connected","service":"soundbridge-backend","timestamp":"2025-06-09T22:52:43.950Z"}
{"level":"info","message":"Redis client ready","service":"soundbridge-backend","timestamp":"2025-06-09T22:52:43.958Z"}
{"level":"info","message":"Redis connection established","service":"soundbridge-backend","timestamp":"2025-06-09T22:52:43.959Z"}
{"environment":"development","level":"info","message":"🚀 SoundBridge AI Backend started successfully","port":"3002","service":"soundbridge-backend","timestamp":"2025-06-09T22:52:43.967Z","version":"1.0.0"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:00.688Z"}
{"level":"info","message":"Redis client connected","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:00.733Z"}
{"level":"info","message":"Redis client ready","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:00.739Z"}
{"level":"info","message":"Redis connection established","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:00.740Z"}
{"environment":"development","level":"info","message":"🚀 SoundBridge AI Backend started successfully","port":"3002","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:00.744Z","version":"1.0.0"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:14.646Z"}
{"level":"info","message":"Redis client connected","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:14.697Z"}
{"level":"info","message":"Redis client ready","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:14.702Z"}
{"level":"info","message":"Redis connection established","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:14.702Z"}
{"environment":"development","level":"info","message":"🚀 SoundBridge AI Backend started successfully","port":"3002","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:14.708Z","version":"1.0.0"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:27.376Z"}
{"level":"info","message":"Redis client connected","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:27.420Z"}
{"level":"info","message":"Redis client ready","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:27.426Z"}
{"level":"info","message":"Redis connection established","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:27.426Z"}
{"environment":"development","level":"info","message":"🚀 SoundBridge AI Backend started successfully","port":"3002","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:27.430Z","version":"1.0.0"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:39.221Z"}
{"level":"info","message":"Redis client connected","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:39.264Z"}
{"level":"info","message":"Redis client ready","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:39.267Z"}
{"level":"info","message":"Redis connection established","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:39.267Z"}
{"environment":"development","level":"info","message":"🚀 SoundBridge AI Backend started successfully","port":"3002","service":"soundbridge-backend","timestamp":"2025-06-09T22:53:39.274Z","version":"1.0.0"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T22:55:26.375Z"}
{"level":"info","message":"Redis client connected","service":"soundbridge-backend","timestamp":"2025-06-09T22:55:26.423Z"}
{"level":"info","message":"Redis client ready","service":"soundbridge-backend","timestamp":"2025-06-09T22:55:26.430Z"}
{"level":"info","message":"Redis connection established","service":"soundbridge-backend","timestamp":"2025-06-09T22:55:26.431Z"}
{"environment":"development","level":"info","message":"🚀 SoundBridge AI Backend started successfully","port":"3002","service":"soundbridge-backend","timestamp":"2025-06-09T22:55:26.435Z","version":"1.0.0"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.668Z","url":"/api/health","userAgent":"axios/1.9.0"}
{"dbConnected":true,"level":"info","message":"Health check performed","service":"soundbridge-backend","status":"healthy","timestamp":"2025-06-09T22:56:41.707Z","uptime":77.1033814}
{"duration":"41ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":200,"timestamp":"2025-06-09T22:56:41.709Z","url":"/"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.767Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.795Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.799Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.802Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.805Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.807Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.808Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.808Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.809Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.810Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.811Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.812Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.815Z","userAgent":"axios/1.9.0"}
{"duration":"4ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.816Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.817Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.818Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.818Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.820Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.821Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.821Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.822Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.823Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.823Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.824Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.825Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.825Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.826Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.826Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.827Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.828Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.828Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.829Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.830Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.832Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.833Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.834Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.835Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.836Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.838Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.839Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.840Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.841Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.842Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.843Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.843Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.844Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.845Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.845Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.846Z","userAgent":"axios/1.9.0"}
{"duration":"4ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.849Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.851Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.852Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.852Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.853Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.854Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.855Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.856Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.858Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.859Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.860Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.861Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.862Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.862Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.863Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.865Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.867Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.868Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.869Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.870Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.871Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.871Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.873Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.874Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.875Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.876Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.877Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.877Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.878Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.880Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.881Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.884Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.885Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.885Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.886Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.887Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.888Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.889Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.890Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.891Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.892Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.893Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.893Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.894Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.895Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.896Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.896Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.899Z","userAgent":"axios/1.9.0"}
{"duration":"4ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.900Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.902Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.903Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.903Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.905Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.906Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.906Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.907Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.908Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.908Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.909Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.910Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.910Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.911Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.912Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.912Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.914Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.917Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.917Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.918Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.920Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.920Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.922Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.923Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.923Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.924Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.925Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.925Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.926Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.927Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.927Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.928Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.929Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.929Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.931Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.933Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.934Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.935Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.935Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.936Z","url":"/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.936Z","url":"/api/auth/register","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/register"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:41.939Z","userAgent":"axios/1.9.0"}
{"duration":"4ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:41.940Z","url":"/register"}
{"email":"<EMAIL>","error":"\n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser0\",\n         firstName: \"Load\",\n         lastName: \"User0\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$n.VeSsNg93OlDygx5CGul.P9fY3ZEGctgvWroR.MXXgU.lCIe89Oq\",\n             salt: \"$2a$12$n.VeSsNg93OlDygx5CGul.\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","level":"error","message":"User registration failed","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.628Z"}
{"context":{"ip":"::1","method":"POST","url":"/api/auth/register","userAgent":"axios/1.9.0"},"level":"error","message":"Application error \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser0\",\n         firstName: \"Load\",\n         lastName: \"User0\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$n.VeSsNg93OlDygx5CGul.P9fY3ZEGctgvWroR.MXXgU.lCIe89Oq\",\n             salt: \"$2a$12$n.VeSsNg93OlDygx5CGul.\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","service":"soundbridge-backend","stack":"PrismaClientValidationError: \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser0\",\n         firstName: \"Load\",\n         lastName: \"User0\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$n.VeSsNg93OlDygx5CGul.P9fY3ZEGctgvWroR.MXXgU.lCIe89Oq\",\n             salt: \"$2a$12$n.VeSsNg93OlDygx5CGul.\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.\n    at wn (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at Function.create (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:12)\n    at Function.register (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\services\\auth.service.ts:81:20)\n    at <anonymous> (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\routes\\auth.ts:65:26)","timestamp":"2025-06-09T22:56:43.633Z"}
{"duration":"1868ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":500,"timestamp":"2025-06-09T22:56:43.635Z","url":"/api/auth/register"}
{"email":"<EMAIL>","error":"\n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser1\",\n         firstName: \"Load\",\n         lastName: \"User1\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$J/2XE0PyPct6T/ch2sEkqOYlVDpkbduVCX2QHloj4cJidlhuddh9C\",\n             salt: \"$2a$12$J/2XE0PyPct6T/ch2sEkqO\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","level":"error","message":"User registration failed","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.649Z"}
{"context":{"ip":"::1","method":"POST","url":"/api/auth/register","userAgent":"axios/1.9.0"},"level":"error","message":"Application error \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser1\",\n         firstName: \"Load\",\n         lastName: \"User1\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$J/2XE0PyPct6T/ch2sEkqOYlVDpkbduVCX2QHloj4cJidlhuddh9C\",\n             salt: \"$2a$12$J/2XE0PyPct6T/ch2sEkqO\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","service":"soundbridge-backend","stack":"PrismaClientValidationError: \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser1\",\n         firstName: \"Load\",\n         lastName: \"User1\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$J/2XE0PyPct6T/ch2sEkqOYlVDpkbduVCX2QHloj4cJidlhuddh9C\",\n             salt: \"$2a$12$J/2XE0PyPct6T/ch2sEkqO\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.\n    at wn (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at Function.create (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:12)\n    at Function.register (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\services\\auth.service.ts:81:20)\n    at <anonymous> (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\routes\\auth.ts:65:26)","timestamp":"2025-06-09T22:56:43.652Z"}
{"duration":"1859ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":500,"timestamp":"2025-06-09T22:56:43.654Z","url":"/api/auth/register"}
{"email":"<EMAIL>","error":"\n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser4\",\n         firstName: \"Load\",\n         lastName: \"User4\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$lt1JjffhXFNRe1t5USFZXe3o.0pxSiTHMjqT7CgE6FoPD/9RY.n7q\",\n             salt: \"$2a$12$lt1JjffhXFNRe1t5USFZXe\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","level":"error","message":"User registration failed","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.659Z"}
{"context":{"ip":"::1","method":"POST","url":"/api/auth/register","userAgent":"axios/1.9.0"},"level":"error","message":"Application error \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser4\",\n         firstName: \"Load\",\n         lastName: \"User4\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$lt1JjffhXFNRe1t5USFZXe3o.0pxSiTHMjqT7CgE6FoPD/9RY.n7q\",\n             salt: \"$2a$12$lt1JjffhXFNRe1t5USFZXe\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","service":"soundbridge-backend","stack":"PrismaClientValidationError: \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser4\",\n         firstName: \"Load\",\n         lastName: \"User4\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$lt1JjffhXFNRe1t5USFZXe3o.0pxSiTHMjqT7CgE6FoPD/9RY.n7q\",\n             salt: \"$2a$12$lt1JjffhXFNRe1t5USFZXe\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.\n    at wn (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at Function.create (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:12)\n    at Function.register (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\services\\auth.service.ts:81:20)\n    at <anonymous> (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\routes\\auth.ts:65:26)","timestamp":"2025-06-09T22:56:43.661Z"}
{"duration":"1858ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":500,"timestamp":"2025-06-09T22:56:43.663Z","url":"/api/auth/register"}
{"email":"<EMAIL>","error":"\n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser3\",\n         firstName: \"Load\",\n         lastName: \"User3\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$DXQm4M3gXutqjA5d/hyU7uZPGQRbUtfpRZTBJm5b35GrPWVCIYaGm\",\n             salt: \"$2a$12$DXQm4M3gXutqjA5d/hyU7u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","level":"error","message":"User registration failed","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.667Z"}
{"context":{"ip":"::1","method":"POST","url":"/api/auth/register","userAgent":"axios/1.9.0"},"level":"error","message":"Application error \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser3\",\n         firstName: \"Load\",\n         lastName: \"User3\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$DXQm4M3gXutqjA5d/hyU7uZPGQRbUtfpRZTBJm5b35GrPWVCIYaGm\",\n             salt: \"$2a$12$DXQm4M3gXutqjA5d/hyU7u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","service":"soundbridge-backend","stack":"PrismaClientValidationError: \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser3\",\n         firstName: \"Load\",\n         lastName: \"User3\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$DXQm4M3gXutqjA5d/hyU7uZPGQRbUtfpRZTBJm5b35GrPWVCIYaGm\",\n             salt: \"$2a$12$DXQm4M3gXutqjA5d/hyU7u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.\n    at wn (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at Function.create (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:12)\n    at Function.register (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\services\\auth.service.ts:81:20)\n    at <anonymous> (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\routes\\auth.ts:65:26)","timestamp":"2025-06-09T22:56:43.669Z"}
{"duration":"1869ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":500,"timestamp":"2025-06-09T22:56:43.671Z","url":"/api/auth/register"}
{"email":"<EMAIL>","error":"\n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser2\",\n         firstName: \"Load\",\n         lastName: \"User2\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$UKayY8doTTsVfZ.5WXDl2uEmQXo5Dl37Ew7./XG43HizRvv/GG3Zu\",\n             salt: \"$2a$12$UKayY8doTTsVfZ.5WXDl2u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","level":"error","message":"User registration failed","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.674Z"}
{"context":{"ip":"::1","method":"POST","url":"/api/auth/register","userAgent":"axios/1.9.0"},"level":"error","message":"Application error \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser2\",\n         firstName: \"Load\",\n         lastName: \"User2\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$UKayY8doTTsVfZ.5WXDl2uEmQXo5Dl37Ew7./XG43HizRvv/GG3Zu\",\n             salt: \"$2a$12$UKayY8doTTsVfZ.5WXDl2u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","service":"soundbridge-backend","stack":"PrismaClientValidationError: \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser2\",\n         firstName: \"Load\",\n         lastName: \"User2\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$UKayY8doTTsVfZ.5WXDl2uEmQXo5Dl37Ew7./XG43HizRvv/GG3Zu\",\n             salt: \"$2a$12$UKayY8doTTsVfZ.5WXDl2u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.\n    at wn (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at Function.create (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:12)\n    at Function.register (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\services\\auth.service.ts:81:20)\n    at <anonymous> (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\routes\\auth.ts:65:26)","timestamp":"2025-06-09T22:56:43.676Z"}
{"duration":"1879ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":500,"timestamp":"2025-06-09T22:56:43.678Z","url":"/api/auth/register"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.726Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.727Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.727Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.728Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.729Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.729Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.731Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.733Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.734Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.735Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.736Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.736Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.738Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.739Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.739Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.741Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.741Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.742Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.743Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.743Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.744Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.745Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.746Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.746Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.749Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.750Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.750Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.751Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.752Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.752Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.753Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.755Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.755Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.757Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.758Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.758Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.758Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.759Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.759Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.760Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.761Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.761Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.762Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.763Z","userAgent":"axios/1.9.0"}
{"duration":"4ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.766Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.767Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.767Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.768Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.769Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.769Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.770Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.771Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.772Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.772Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.773Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.774Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.774Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.776Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.777Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.777Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.778Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.779Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.779Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.780Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.783Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.783Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.784Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.785Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.785Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.786Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.786Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.787Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.791Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.791Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.792Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.792Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.793Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.793Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.794Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.795Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.796Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.797Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.799Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.800Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.801Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.801Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.802Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.803Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.804Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.804Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.806Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.807Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.807Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.808Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.808Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.809Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.810Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.811Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.812Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.812Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.813Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.814Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.816Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.817Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.817Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.818Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.819Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.819Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.820Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.822Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.822Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.823Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.824Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.824Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.825Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.825Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.826Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.827Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.827Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.828Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.828Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.829Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.829Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.832Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.833Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.834Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.835Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.835Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.836Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.837Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.838Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.838Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.840Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.841Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.841Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.842Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.843Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.843Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.844Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.845Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.845Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.849Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.850Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.850Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.851Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/login"},"event":"auth_rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.852Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.852Z","url":"/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.853Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.854Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.855Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.872Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.873Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.874Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.874Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.875Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.875Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.882Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.883Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.884Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.884Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.885Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.885Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.886Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.887Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.887Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.888Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.889Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.890Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.891Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.891Z","userAgent":"axios/1.9.0"}
{"duration":"0ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.891Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.892Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.892Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.893Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.893Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.894Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.894Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.895Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.895Z","userAgent":"axios/1.9.0"}
{"duration":"0ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.895Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.896Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.897Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.899Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.899Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.900Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.900Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.901Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.902Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.902Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.903Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.904Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.904Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.905Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.906Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.906Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.907Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.907Z","userAgent":"axios/1.9.0"}
{"duration":"0ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.907Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.908Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.908Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.909Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.909Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.910Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.910Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.911Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.911Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.912Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.912Z","url":"/api/auth/login","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"POST","path":"/api/auth/login"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.914Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"POST","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:56:43.915Z","url":"/api/auth/login"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T22:57:50.717Z","url":"/api/health","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"GET","path":"/api/health"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:57:50.717Z","userAgent":"axios/1.9.0"}
{"duration":"1ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:57:50.718Z","url":"/api/health"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T22:58:12.832Z","url":"/api/health","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"GET","path":"/api/health"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:58:12.833Z","userAgent":"axios/1.9.0"}
{"duration":"2ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:58:12.834Z","url":"/api/health"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"soundbridge-backend","timestamp":"2025-06-09T22:58:41.155Z","url":"/api/health","userAgent":"axios/1.9.0"}
{"details":{"ip":"::1","method":"GET","path":"/api/health"},"event":"rate_limit_exceeded","ip":"::1","level":"warn","message":"Security event","service":"soundbridge-backend","timestamp":"2025-06-09T22:58:41.156Z","userAgent":"axios/1.9.0"}
{"duration":"3ms","level":"info","message":"Request completed","method":"GET","service":"soundbridge-backend","statusCode":429,"timestamp":"2025-06-09T22:58:41.158Z","url":"/api/health"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"soundbridge-backend","timestamp":"2025-06-09T22:58:50.411Z"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-09T22:59:01.722Z"}
{"level":"info","message":"Redis client connected","service":"soundbridge-backend","timestamp":"2025-06-09T22:59:01.767Z"}
{"level":"info","message":"Redis client ready","service":"soundbridge-backend","timestamp":"2025-06-09T22:59:01.771Z"}
{"level":"info","message":"Redis connection established","service":"soundbridge-backend","timestamp":"2025-06-09T22:59:01.772Z"}
{"environment":"development","level":"info","message":"🚀 SoundBridge AI Backend started successfully","port":"3002","service":"soundbridge-backend","timestamp":"2025-06-09T22:59:01.777Z","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"soundbridge-backend","timestamp":"2025-06-09T22:59:03.264Z"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:04:29.762Z","userId":"cmbqw3z4a000a6o9lf1llxlsj"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:07:18.935Z","userId":"cmbqw7lfr000myxthmx4qprav"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:17:09.972Z","userId":"cmbqwk9pe000a7i4jkc6bczcn"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:19:56.573Z","userId":"cmbqwnu8e000abck7b5u0eash"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:26:55.856Z","userId":"cmbqwwtu0000arv2i7kw7ie5h"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:36:48.512Z","userId":"cmbqx9j57000ai1m4gog0r9rm"}
{"environment":"development","level":"info","message":"Logger initialized","service":"soundbridge-backend","timestamp":"2025-06-12T20:42:14.442Z"}
{"error":"Database connection failed","level":"error","message":"❌ Failed to start server","service":"soundbridge-backend","stack":"Error: Database connection failed\n    at startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\index.ts:91:13)","timestamp":"2025-06-12T20:42:18.556Z"}
