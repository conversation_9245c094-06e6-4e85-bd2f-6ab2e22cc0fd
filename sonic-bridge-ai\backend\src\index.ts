// SoundBridge AI Backend - Main entry point

import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import swaggerUi from 'swagger-ui-express';
import { createAPIResponse } from '@soundbridge-ai/shared';
import { checkDatabaseConnection, connectRedis, checkRedisConnection, disconnectDatabase, disconnectRedis } from './database/connection';
import { setupLogger, requestLogger, logger } from './middleware/logger';
import { rateLimitMiddleware } from './middleware/rateLimiting';
import { errorHandler } from './middleware/errorHandler';
import { swaggerSpec } from './config/swagger';
import apiRoutes from './routes';
import { GoogleOAuthService } from './services/googleOAuth.service';

const app = express();
const PORT = process.env.API_PORT || process.env.PORT || 3002;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:8080', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
}));

// General middleware
app.use(compression());
app.use(requestLogger);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
app.use(rateLimitMiddleware);

// API Documentation with Swagger
app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'SoundBridge AI API Documentation'
}));

// Serve OpenAPI spec as JSON
app.get('/docs/openapi.json', (_req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerSpec);
});

// API routes
app.use('/api', apiRoutes);

// Legacy health endpoint (redirect to new location)
app.get('/health', (_req, res) => {
  res.redirect(301, '/api/health');
});

// 404 handler for unmatched routes
app.use('*', (req, res) => {
  res.status(404).json(
    createAPIResponse(false, null, `Route ${req.method} ${req.originalUrl} not found`)
  );
});

// Global error handler
app.use(errorHandler);

// Initialize database and start server
async function startServer() {
  try {
    // Initialize logger
    await setupLogger();

    // Initialize database connection (optional for OAuth testing)
    const dbConnected = await checkDatabaseConnection();
    if (!dbConnected) {
      logger.warn('Database connection failed - running in limited mode for OAuth testing');
      console.log('⚠️  Database not available - OAuth testing mode enabled');
    } else {
      logger.info('Database connection established');
      console.log('✅ Database connected');
    }

    // Initialize Redis connection (optional for OAuth testing)
    try {
      await connectRedis();
      const redisConnected = await checkRedisConnection();
      if (!redisConnected) {
        logger.warn('Redis connection failed - session storage disabled');
        console.log('⚠️  Redis not available - session storage disabled');
      } else {
        logger.info('Redis connection established');
        console.log('✅ Redis connected');
      }
    } catch (error) {
      logger.warn('Redis connection failed - continuing without Redis', { error: error instanceof Error ? error.message : 'Unknown error' });
      console.log('⚠️  Redis not available - continuing without session storage');
    }

    // Initialize Google OAuth
    try {
      GoogleOAuthService.initialize();
      logger.info('Google OAuth service initialized successfully');
    } catch (error) {
      logger.warn('Google OAuth service initialization failed - OAuth features will be disabled', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Start the server
    app.listen(PORT, () => {
      logger.info('🚀 SoundBridge AI Backend started successfully', {
        port: PORT,
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0'
      });

      console.log(`🚀 SoundBridge AI Backend running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
      console.log(`📚 API docs: http://localhost:${PORT}/docs`);
      console.log(`🔗 API base: http://localhost:${PORT}/api`);
      console.log(`🗄️ Database: Connected and ready`);
      console.log(`🔴 Redis: Connected and ready`);
      console.log(`🛡️ Security: Rate limiting and validation enabled`);
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    logger.error('❌ Failed to start server', { error: errorMessage, stack: errorStack });
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown handling
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await disconnectDatabase();
  await disconnectRedis();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await disconnectDatabase();
  await disconnectRedis();
  process.exit(0);
});

// Start the application
startServer();

// Export app for testing
export { app };
