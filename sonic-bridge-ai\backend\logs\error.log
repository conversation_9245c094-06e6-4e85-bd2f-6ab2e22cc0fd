{"email":"<EMAIL>","error":"\n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser0\",\n         firstName: \"Load\",\n         lastName: \"User0\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$n.VeSsNg93OlDygx5CGul.P9fY3ZEGctgvWroR.MXXgU.lCIe89Oq\",\n             salt: \"$2a$12$n.VeSsNg93OlDygx5CGul.\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","level":"error","message":"User registration failed","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.628Z"}
{"context":{"ip":"::1","method":"POST","url":"/api/auth/register","userAgent":"axios/1.9.0"},"level":"error","message":"Application error \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser0\",\n         firstName: \"Load\",\n         lastName: \"User0\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$n.VeSsNg93OlDygx5CGul.P9fY3ZEGctgvWroR.MXXgU.lCIe89Oq\",\n             salt: \"$2a$12$n.VeSsNg93OlDygx5CGul.\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","service":"soundbridge-backend","stack":"PrismaClientValidationError: \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser0\",\n         firstName: \"Load\",\n         lastName: \"User0\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$n.VeSsNg93OlDygx5CGul.P9fY3ZEGctgvWroR.MXXgU.lCIe89Oq\",\n             salt: \"$2a$12$n.VeSsNg93OlDygx5CGul.\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.\n    at wn (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at Function.create (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:12)\n    at Function.register (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\services\\auth.service.ts:81:20)\n    at <anonymous> (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\routes\\auth.ts:65:26)","timestamp":"2025-06-09T22:56:43.633Z"}
{"email":"<EMAIL>","error":"\n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser1\",\n         firstName: \"Load\",\n         lastName: \"User1\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$J/2XE0PyPct6T/ch2sEkqOYlVDpkbduVCX2QHloj4cJidlhuddh9C\",\n             salt: \"$2a$12$J/2XE0PyPct6T/ch2sEkqO\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","level":"error","message":"User registration failed","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.649Z"}
{"context":{"ip":"::1","method":"POST","url":"/api/auth/register","userAgent":"axios/1.9.0"},"level":"error","message":"Application error \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser1\",\n         firstName: \"Load\",\n         lastName: \"User1\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$J/2XE0PyPct6T/ch2sEkqOYlVDpkbduVCX2QHloj4cJidlhuddh9C\",\n             salt: \"$2a$12$J/2XE0PyPct6T/ch2sEkqO\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","service":"soundbridge-backend","stack":"PrismaClientValidationError: \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser1\",\n         firstName: \"Load\",\n         lastName: \"User1\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$J/2XE0PyPct6T/ch2sEkqOYlVDpkbduVCX2QHloj4cJidlhuddh9C\",\n             salt: \"$2a$12$J/2XE0PyPct6T/ch2sEkqO\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.\n    at wn (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at Function.create (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:12)\n    at Function.register (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\services\\auth.service.ts:81:20)\n    at <anonymous> (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\routes\\auth.ts:65:26)","timestamp":"2025-06-09T22:56:43.652Z"}
{"email":"<EMAIL>","error":"\n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser4\",\n         firstName: \"Load\",\n         lastName: \"User4\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$lt1JjffhXFNRe1t5USFZXe3o.0pxSiTHMjqT7CgE6FoPD/9RY.n7q\",\n             salt: \"$2a$12$lt1JjffhXFNRe1t5USFZXe\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","level":"error","message":"User registration failed","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.659Z"}
{"context":{"ip":"::1","method":"POST","url":"/api/auth/register","userAgent":"axios/1.9.0"},"level":"error","message":"Application error \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser4\",\n         firstName: \"Load\",\n         lastName: \"User4\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$lt1JjffhXFNRe1t5USFZXe3o.0pxSiTHMjqT7CgE6FoPD/9RY.n7q\",\n             salt: \"$2a$12$lt1JjffhXFNRe1t5USFZXe\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","service":"soundbridge-backend","stack":"PrismaClientValidationError: \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser4\",\n         firstName: \"Load\",\n         lastName: \"User4\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$lt1JjffhXFNRe1t5USFZXe3o.0pxSiTHMjqT7CgE6FoPD/9RY.n7q\",\n             salt: \"$2a$12$lt1JjffhXFNRe1t5USFZXe\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.\n    at wn (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at Function.create (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:12)\n    at Function.register (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\services\\auth.service.ts:81:20)\n    at <anonymous> (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\routes\\auth.ts:65:26)","timestamp":"2025-06-09T22:56:43.661Z"}
{"email":"<EMAIL>","error":"\n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser3\",\n         firstName: \"Load\",\n         lastName: \"User3\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$DXQm4M3gXutqjA5d/hyU7uZPGQRbUtfpRZTBJm5b35GrPWVCIYaGm\",\n             salt: \"$2a$12$DXQm4M3gXutqjA5d/hyU7u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","level":"error","message":"User registration failed","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.667Z"}
{"context":{"ip":"::1","method":"POST","url":"/api/auth/register","userAgent":"axios/1.9.0"},"level":"error","message":"Application error \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser3\",\n         firstName: \"Load\",\n         lastName: \"User3\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$DXQm4M3gXutqjA5d/hyU7uZPGQRbUtfpRZTBJm5b35GrPWVCIYaGm\",\n             salt: \"$2a$12$DXQm4M3gXutqjA5d/hyU7u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","service":"soundbridge-backend","stack":"PrismaClientValidationError: \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser3\",\n         firstName: \"Load\",\n         lastName: \"User3\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$DXQm4M3gXutqjA5d/hyU7uZPGQRbUtfpRZTBJm5b35GrPWVCIYaGm\",\n             salt: \"$2a$12$DXQm4M3gXutqjA5d/hyU7u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.\n    at wn (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at Function.create (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:12)\n    at Function.register (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\services\\auth.service.ts:81:20)\n    at <anonymous> (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\routes\\auth.ts:65:26)","timestamp":"2025-06-09T22:56:43.669Z"}
{"email":"<EMAIL>","error":"\n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser2\",\n         firstName: \"Load\",\n         lastName: \"User2\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$UKayY8doTTsVfZ.5WXDl2uEmQXo5Dl37Ew7./XG43HizRvv/GG3Zu\",\n             salt: \"$2a$12$UKayY8doTTsVfZ.5WXDl2u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","level":"error","message":"User registration failed","service":"soundbridge-backend","timestamp":"2025-06-09T22:56:43.674Z"}
{"context":{"ip":"::1","method":"POST","url":"/api/auth/register","userAgent":"axios/1.9.0"},"level":"error","message":"Application error \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser2\",\n         firstName: \"Load\",\n         lastName: \"User2\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$UKayY8doTTsVfZ.5WXDl2uEmQXo5Dl37Ew7./XG43HizRvv/GG3Zu\",\n             salt: \"$2a$12$UKayY8doTTsVfZ.5WXDl2u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.","service":"soundbridge-backend","stack":"PrismaClientValidationError: \n\u001b[31mInvalid \u001b[1m`prisma.user.create()`\u001b[22m invocation in\u001b[39m\n\u001b[4mc:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:30\u001b[24m\n\n  \u001b[2m\u001b[90m30\u001b[39m   authCreateData = \u001b[34m{\u001b[39m passwordHash\u001b[34m,\u001b[39m salt \u001b[34m}\u001b[39m\u001b[34m;\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m31\u001b[39m \u001b[34m}\u001b[39m\u001b[22m\n  \u001b[2m\u001b[90m32\u001b[39m \u001b[22m\n\u001b[1m\u001b[31m→\u001b[39m\u001b[22m \u001b[2m\u001b[90m33\u001b[39m \u001b[36mreturn\u001b[39m \u001b[36mawait\u001b[39m prisma\u001b[34m.\u001b[39muser\u001b[34m.\u001b[39m\u001b[36mcreate\u001b[39m\u001b[34m(\u001b[39m\u001b[22m{\n       data: {\n         email: \"<EMAIL>\",\n         username: \"loaduser2\",\n         firstName: \"Load\",\n         lastName: \"User2\",\n         \u001b[31macceptTerms\u001b[39m: true,\n         \u001b[31m~~~~~~~~~~~\u001b[39m\n         auth: {\n           create: {\n             passwordHash: \"$2a$12$UKayY8doTTsVfZ.5WXDl2uEmQXo5Dl37Ew7./XG43HizRvv/GG3Zu\",\n             salt: \"$2a$12$UKayY8doTTsVfZ.5WXDl2u\"\n           }\n         },\n         preferences: {\n           create: {\n             audioQuality: \"high\",\n             autoPlay: true,\n             publicPlaylists: true,\n             emailNotifications: true,\n             pushNotifications: true\n           }\n         },\n     \u001b[32m?\u001b[39m   \u001b[32mid\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mavatar\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mString | Null\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misVerified\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32misActive\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mBoolean\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mcreatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mupdatedAt\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mDateTime\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplatforms\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlatformConnectionCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCreateNestedManyWithoutOwnerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mactivities\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserActivityCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowing\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowerInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mfollowers\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserConnectionCreateNestedManyWithoutFollowingInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mhumRequests\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mHumRecognitionRequestCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mmusicProfile\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mUserMusicProfileCreateNestedOneWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mlisteningHistory\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mListeningHistoryCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32mplaylistCollaborations\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistCollaboratorCreateNestedManyWithoutUserInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32maddedTracks\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistTrackCreateNestedManyWithoutAddedByInput\u001b[39m,\n     \u001b[32m?\u001b[39m   \u001b[32msharedPlaylists\u001b[39m\u001b[32m?\u001b[39m\u001b[32m: \u001b[39m\u001b[32mPlaylistShareCreateNestedManyWithoutSharedByInput\u001b[39m\n       },\n       include: {\n         auth: true,\n         preferences: true\n       }\n     }\u001b[2m)\u001b[22m\n\nUnknown argument `\u001b[31macceptTerms\u001b[39m`. Available options are listed in \u001b[32mgreen\u001b[39m.\n    at wn (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at Function.create (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\database\\models\\user.model.ts:33:12)\n    at Function.register (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\services\\auth.service.ts:81:20)\n    at <anonymous> (c:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\routes\\auth.ts:65:26)","timestamp":"2025-06-09T22:56:43.676Z"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:04:29.762Z","userId":"cmbqw3z4a000a6o9lf1llxlsj"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:07:18.935Z","userId":"cmbqw7lfr000myxthmx4qprav"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:17:09.972Z","userId":"cmbqwk9pe000a7i4jkc6bczcn"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:19:56.573Z","userId":"cmbqwnu8e000abck7b5u0eash"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:26:55.856Z","userId":"cmbqwwtu0000arv2i7kw7ie5h"}
{"error":"SPOTIFY connection already exists for this user","level":"error","message":"Failed to create platform connection","platform":"SPOTIFY","service":"soundbridge-backend","timestamp":"2025-06-10T19:36:48.512Z","userId":"cmbqx9j57000ai1m4gog0r9rm"}
{"error":"Database connection failed","level":"error","message":"❌ Failed to start server","service":"soundbridge-backend","stack":"Error: Database connection failed\n    at startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\index.ts:91:13)","timestamp":"2025-06-12T20:42:18.556Z"}
{"error":"Database connection failed","level":"error","message":"❌ Failed to start server","service":"soundbridge-backend","stack":"Error: Database connection failed\n    at startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\B1\\sonic-bridge-ai\\backend\\src\\index.ts:91:13)","timestamp":"2025-06-12T20:54:10.156Z"}
