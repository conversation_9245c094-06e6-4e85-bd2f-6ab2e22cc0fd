# SoundBridge AI - OAuth Integration Testing Guide

## 🎯 Overview

This guide provides comprehensive testing procedures for Google and Spotify OAuth integration (SBA-006 & SBA-007) using your real Gmail and Spotify accounts.

## ✅ Prerequisites Checklist

### 🔧 Environment Setup
- [ ] Backend server dependencies installed (`npm install`)
- [ ] PostgreSQL database running on port 5433
- [ ] Redis server running on port 6380
- [ ] Environment variables configured in `.env` file

### 🔐 OAuth Applications Setup
- [ ] Google OAuth 2.0 application created in Google Cloud Console
- [ ] Spotify application created in Spotify Developer Dashboard
- [ ] Redirect URIs configured correctly
- [ ] Client credentials obtained and configured

## 📋 Step-by-Step Testing Process

### Step 1: Configure OAuth Credentials

```bash
# Run the interactive setup script
cd sonic-bridge-ai
node scripts/setup-oauth-credentials.js
```

**What this script does:**
- Guides you through Google OAuth setup
- Guides you through Spotify OAuth setup
- Updates your `.env` file with real credentials
- Validates credential formats

### Step 2: Start Backend Server

```bash
cd sonic-bridge-ai/backend
npm run dev
```

**Expected output:**
- Server starts on port 3002
- Database connections established
- OAuth services initialized

### Step 3: Run OAuth Integration Tests

```bash
cd sonic-bridge-ai
node tests/oauth-integration-test.js
```

## 🧪 Test Scenarios

### 🔵 Google OAuth Flow Test

**Test Steps:**
1. **Auth URL Generation**
   - Script generates Google OAuth URL
   - URL includes proper client_id, redirect_uri, scope, state
   - State parameter includes security token

2. **Manual Browser Testing**
   - Open generated URL in browser
   - Sign in with your Gmail account
   - Grant requested permissions (profile, email)
   - Browser redirects to callback URL

3. **Callback Processing**
   - Extract authorization code from callback URL
   - Exchange code for access token
   - Retrieve user profile from Google
   - Generate JWT token for SoundBridge

4. **Token Validation**
   - Validate JWT token with backend
   - Retrieve user profile using token
   - Verify user data integrity

### 🟢 Spotify OAuth Flow Test

**Test Steps:**
1. **Auth URL Generation**
   - Script generates Spotify OAuth URL with PKCE
   - URL includes code_challenge for security
   - Proper scopes for playlist access

2. **Manual Browser Testing**
   - Open generated URL in browser
   - Sign in with your Spotify account
   - Grant requested permissions
   - Browser redirects to callback URL

3. **Callback Processing**
   - Extract authorization code from callback URL
   - Exchange code for access token using PKCE
   - Retrieve user profile from Spotify
   - Store Spotify connection data

4. **Token Validation**
   - Validate JWT token with backend
   - Retrieve user profile using token
   - Verify Spotify integration data

### 🔗 Account Linking Test

**Test Steps:**
1. **Link Spotify to Google Account**
   - Use Google JWT token to authenticate
   - Link Spotify account to existing Google user
   - Verify both connections in database

2. **Cross-Platform Access**
   - Test accessing both Google and Spotify data
   - Verify user can switch between platforms
   - Check data consistency

## 📊 Expected Test Results

### ✅ Success Criteria

**Google OAuth:**
- [ ] Auth URL generated successfully
- [ ] User can sign in with Gmail account
- [ ] Callback processed without errors
- [ ] JWT token received and validated
- [ ] User profile retrieved correctly
- [ ] Email and basic profile data available

**Spotify OAuth:**
- [ ] Auth URL generated with PKCE
- [ ] User can sign in with Spotify account
- [ ] Callback processed with PKCE validation
- [ ] JWT token received and validated
- [ ] User profile retrieved correctly
- [ ] Spotify user ID and display name available

**Integration:**
- [ ] Both OAuth flows work independently
- [ ] Account linking functions correctly
- [ ] JWT tokens are valid and secure
- [ ] User data is stored properly in database
- [ ] No security vulnerabilities detected

### ❌ Common Issues & Solutions

**Issue: "OAuth configuration is missing"**
- **Solution:** Run `node scripts/setup-oauth-credentials.js` to configure credentials

**Issue: "Invalid redirect URI"**
- **Solution:** Ensure redirect URIs in OAuth apps match:
  - Google: `http://localhost:3002/auth/google/callback`
  - Spotify: `http://localhost:3002/auth/spotify/callback`

**Issue: "Database connection failed"**
- **Solution:** Start PostgreSQL and Redis services
- Check connection strings in `.env` file

**Issue: "CORS errors in browser"**
- **Solution:** Ensure CORS_ORIGIN in `.env` matches frontend URL

## 🔒 Security Testing

### Security Checklist
- [ ] State parameter prevents CSRF attacks
- [ ] PKCE implementation for Spotify OAuth
- [ ] JWT tokens have proper expiration
- [ ] Sensitive data not logged or exposed
- [ ] Redirect URI validation working
- [ ] Token refresh mechanism secure

### Security Test Commands

```bash
# Test invalid state parameter
curl -X GET "http://localhost:3002/auth/google/callback?code=test&state=invalid"

# Test missing parameters
curl -X GET "http://localhost:3002/auth/google/callback"

# Test token validation
curl -X GET "http://localhost:3002/auth/profile" \
  -H "Authorization: Bearer invalid_token"
```

## 📈 Performance Testing

### Performance Metrics
- [ ] OAuth URL generation: < 100ms
- [ ] Callback processing: < 2 seconds
- [ ] Token validation: < 200ms
- [ ] User profile retrieval: < 500ms

### Load Testing
```bash
# Test concurrent OAuth requests
for i in {1..10}; do
  curl -X POST "http://localhost:3002/auth/google/login" &
done
```

## 📝 Test Report Template

```
# OAuth Integration Test Report
Date: [DATE]
Tester: [YOUR_NAME]
Environment: Development

## Google OAuth Results
- Auth URL Generation: ✅/❌
- Browser Login: ✅/❌
- Callback Processing: ✅/❌
- Token Validation: ✅/❌
- Profile Retrieval: ✅/❌

## Spotify OAuth Results
- Auth URL Generation: ✅/❌
- Browser Login: ✅/❌
- Callback Processing: ✅/❌
- Token Validation: ✅/❌
- Profile Retrieval: ✅/❌

## Integration Results
- Account Linking: ✅/❌
- Cross-Platform Access: ✅/❌
- Security Tests: ✅/❌
- Performance Tests: ✅/❌

## Issues Found
[List any issues discovered]

## Recommendations
[List recommendations for improvements]

## Overall Status
- Google OAuth: PASS/FAIL
- Spotify OAuth: PASS/FAIL
- Ready for Production: YES/NO
```

## 🚀 Next Steps After Testing

1. **If tests pass:**
   - Document any configuration notes
   - Prepare for frontend integration
   - Plan user acceptance testing

2. **If tests fail:**
   - Review error logs
   - Check OAuth app configurations
   - Verify environment variables
   - Re-run specific test scenarios

## 📞 Support

If you encounter issues during testing:
1. Check the console logs for detailed error messages
2. Verify OAuth app configurations in Google/Spotify dashboards
3. Ensure all environment variables are set correctly
4. Review the backend server logs for additional context

---

**Document Version:** 1.0  
**Last Updated:** [DATE]  
**Related Tickets:** SBA-006, SBA-007
