/**
 * SoundBridge AI - OAuth Credentials Setup Script
 * Interactive script to configure Google and Spotify OAuth credentials
 * 
 * Usage: node setup-oauth-credentials.js
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (prompt) => {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
};

const logStep = (step, status, details = '') => {
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⏳';
  console.log(`${statusIcon} ${step}${details ? ': ' + details : ''}`);
};

const logHeader = (title) => {
  console.log('\n' + '='.repeat(60));
  console.log(`🔧 ${title}`);
  console.log('='.repeat(60));
};

async function setupGoogleOAuth() {
  logHeader('Google OAuth 2.0 Setup');
  
  console.log('\n📋 To set up Google OAuth, you need to:');
  console.log('1. Go to https://console.cloud.google.com/');
  console.log('2. Create a new project or select existing one');
  console.log('3. Enable Google+ API');
  console.log('4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"');
  console.log('5. Set Application type to "Web application"');
  console.log('6. Add Authorized redirect URI: http://localhost:3002/auth/google/callback');
  console.log('7. Copy the Client ID and Client Secret');
  
  const proceed = await question('\n❓ Have you completed the Google OAuth setup? (y/n): ');
  
  if (proceed.toLowerCase() !== 'y') {
    console.log('⏸️ Please complete the Google OAuth setup first, then run this script again.');
    return null;
  }
  
  const clientId = await question('\n📝 Enter Google Client ID: ');
  const clientSecret = await question('📝 Enter Google Client Secret: ');
  
  if (!clientId || !clientSecret) {
    console.log('❌ Google OAuth credentials are required');
    return null;
  }
  
  return {
    GOOGLE_CLIENT_ID: clientId,
    GOOGLE_CLIENT_SECRET: clientSecret,
    GOOGLE_REDIRECT_URI: 'http://localhost:3002/auth/google/callback'
  };
}

async function setupSpotifyOAuth() {
  logHeader('Spotify OAuth Setup');
  
  console.log('\n📋 To set up Spotify OAuth, you need to:');
  console.log('1. Go to https://developer.spotify.com/dashboard');
  console.log('2. Log in with your Spotify account');
  console.log('3. Click "Create an App"');
  console.log('4. Fill in app name and description');
  console.log('5. In app settings, add Redirect URI: http://localhost:3002/auth/spotify/callback');
  console.log('6. Copy the Client ID and Client Secret');
  
  const proceed = await question('\n❓ Have you completed the Spotify OAuth setup? (y/n): ');
  
  if (proceed.toLowerCase() !== 'y') {
    console.log('⏸️ Please complete the Spotify OAuth setup first, then run this script again.');
    return null;
  }
  
  const clientId = await question('\n📝 Enter Spotify Client ID: ');
  const clientSecret = await question('📝 Enter Spotify Client Secret: ');
  
  if (!clientId || !clientSecret) {
    console.log('❌ Spotify OAuth credentials are required');
    return null;
  }
  
  return {
    SPOTIFY_CLIENT_ID: clientId,
    SPOTIFY_CLIENT_SECRET: clientSecret,
    SPOTIFY_REDIRECT_URI: 'http://localhost:3002/auth/spotify/callback'
  };
}

async function updateEnvFile(credentials) {
  const envPath = path.join(__dirname, '../backend/.env');
  
  try {
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // Update Google OAuth credentials
    if (credentials.google) {
      envContent = envContent.replace(
        /GOOGLE_CLIENT_ID=.*/,
        `GOOGLE_CLIENT_ID=${credentials.google.GOOGLE_CLIENT_ID}`
      );
      envContent = envContent.replace(
        /GOOGLE_CLIENT_SECRET=.*/,
        `GOOGLE_CLIENT_SECRET=${credentials.google.GOOGLE_CLIENT_SECRET}`
      );
      envContent = envContent.replace(
        /GOOGLE_REDIRECT_URI=.*/,
        `GOOGLE_REDIRECT_URI=${credentials.google.GOOGLE_REDIRECT_URI}`
      );
    }
    
    // Update Spotify OAuth credentials
    if (credentials.spotify) {
      envContent = envContent.replace(
        /SPOTIFY_CLIENT_ID=.*/,
        `SPOTIFY_CLIENT_ID=${credentials.spotify.SPOTIFY_CLIENT_ID}`
      );
      envContent = envContent.replace(
        /SPOTIFY_CLIENT_SECRET=.*/,
        `SPOTIFY_CLIENT_SECRET=${credentials.spotify.SPOTIFY_CLIENT_SECRET}`
      );
      envContent = envContent.replace(
        /SPOTIFY_REDIRECT_URI=.*/,
        `SPOTIFY_REDIRECT_URI=${credentials.spotify.SPOTIFY_REDIRECT_URI}`
      );
    }
    
    fs.writeFileSync(envPath, envContent);
    logStep('Environment file updated', 'PASS', envPath);
    
    return true;
  } catch (error) {
    logStep('Environment file update', 'FAIL', error.message);
    return false;
  }
}

async function validateCredentials(credentials) {
  logHeader('Credential Validation');
  
  // Validate Google credentials
  if (credentials.google) {
    const { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET } = credentials.google;
    
    if (GOOGLE_CLIENT_ID.includes('googleusercontent.com')) {
      logStep('Google Client ID format', 'PASS');
    } else {
      logStep('Google Client ID format', 'FAIL', 'Should end with .googleusercontent.com');
    }
    
    if (GOOGLE_CLIENT_SECRET.length >= 24) {
      logStep('Google Client Secret length', 'PASS');
    } else {
      logStep('Google Client Secret length', 'FAIL', 'Seems too short');
    }
  }
  
  // Validate Spotify credentials
  if (credentials.spotify) {
    const { SPOTIFY_CLIENT_ID, SPOTIFY_CLIENT_SECRET } = credentials.spotify;
    
    if (SPOTIFY_CLIENT_ID.length === 32) {
      logStep('Spotify Client ID format', 'PASS');
    } else {
      logStep('Spotify Client ID format', 'FAIL', 'Should be 32 characters');
    }
    
    if (SPOTIFY_CLIENT_SECRET.length === 32) {
      logStep('Spotify Client Secret format', 'PASS');
    } else {
      logStep('Spotify Client Secret format', 'FAIL', 'Should be 32 characters');
    }
  }
}

async function generateTestInstructions() {
  logHeader('Next Steps - Testing Instructions');
  
  console.log('\n🎯 Your OAuth credentials have been configured!');
  console.log('\n📋 To test the OAuth integration:');
  console.log('');
  console.log('1. Start the backend server:');
  console.log('   cd sonic-bridge-ai/backend');
  console.log('   npm run dev');
  console.log('');
  console.log('2. Run the OAuth integration test:');
  console.log('   cd sonic-bridge-ai');
  console.log('   node tests/oauth-integration-test.js');
  console.log('');
  console.log('3. The test will guide you through:');
  console.log('   ✅ Testing Google OAuth login with your Gmail account');
  console.log('   ✅ Testing Spotify OAuth login with your Spotify account');
  console.log('   ✅ Validating JWT tokens');
  console.log('   ✅ Testing account linking');
  console.log('   ✅ Generating a comprehensive test report');
  console.log('');
  console.log('🔐 Security Notes:');
  console.log('   • Your credentials are stored locally in .env file');
  console.log('   • Never commit .env file to version control');
  console.log('   • Use environment-specific credentials for production');
  console.log('');
  console.log('🚀 Ready to test your OAuth integration!');
}

async function main() {
  console.log('🔧 SoundBridge AI - OAuth Credentials Setup');
  console.log('This script will help you configure Google and Spotify OAuth credentials\n');
  
  try {
    const credentials = {};
    
    // Setup Google OAuth
    const googleCreds = await setupGoogleOAuth();
    if (googleCreds) {
      credentials.google = googleCreds;
    }
    
    // Setup Spotify OAuth
    const spotifyCreds = await setupSpotifyOAuth();
    if (spotifyCreds) {
      credentials.spotify = spotifyCreds;
    }
    
    if (!credentials.google && !credentials.spotify) {
      console.log('\n❌ No credentials provided. Exiting...');
      process.exit(1);
    }
    
    // Validate credentials
    await validateCredentials(credentials);
    
    // Update .env file
    const updated = await updateEnvFile(credentials);
    if (!updated) {
      console.log('\n❌ Failed to update environment file');
      process.exit(1);
    }
    
    // Generate test instructions
    await generateTestInstructions();
    
  } catch (error) {
    console.error('\n💥 Setup failed:', error.message);
  } finally {
    rl.close();
  }
}

// Run setup
if (require.main === module) {
  main();
}

module.exports = {
  setupGoogleOAuth,
  setupSpotifyOAuth,
  updateEnvFile
};
